-- 权限管理系统初始化数据
-- 支持幂等性执行

-- 1. 初始化默认角色
INSERT INTO t_role (role_name, role_description, role_type, create_by)
VALUES
    ('系统管理员', '可访问所有菜单，操作不受限', 'DEFAULT', 'system'),
    ('平台用户', '可使用除"权限管理"外的所有平台功能', 'DEFAULT', 'system'),
    ('访客', '仅可访问首页', 'DEFAULT', 'system')
ON CONFLICT DO NOTHING;

-- 2. 初始化基础菜单结构
-- 根菜单（不在页面显示）
INSERT INTO t_menu (menu_id, menu_name, parent_id, menu_path, sort_order, menu_level)
VALUES ('00', '根菜单', NULL, '/', 0, 0)
ON CONFLICT DO NOTHING;

-- 第一层菜单
INSERT INTO t_menu (menu_id, menu_name, parent_id, menu_path, sort_order, menu_level)
VALUES
    ('01', '首页', NULL, '/workbench', 1, 1),
    ('02', '权限管理', NULL, '/permission', 2, 1)
ON CONFLICT DO NOTHING;

-- 权限管理子菜单
INSERT INTO t_menu (menu_id, menu_name, parent_id, menu_path, sort_order, menu_level)
VALUES
    ('0201', '角色管理', NULL, '/permission/role', 1, 2),
    ('0202', '用户（组）管理', NULL, '/permission/user', 2, 2)
ON CONFLICT DO NOTHING;

-- 3. 为默认角色分配菜单权限
-- 系统管理员拥有所有菜单权限
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r, t_menu m
WHERE r.role_name = '系统管理员' AND r.role_type = 'DEFAULT'
ON CONFLICT DO NOTHING;

-- 访客只能访问首页
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r, t_menu m
WHERE r.role_name = '访客' AND r.role_type = 'DEFAULT' AND m.menu_id = '01'
ON CONFLICT DO NOTHING;

-- 平台用户可以访问除权限管理外的所有菜单
INSERT INTO t_role_menu (role_id, menu_id)
SELECT r.role_id, m.menu_id
FROM t_role r, t_menu m
WHERE r.role_name = '平台用户' AND r.role_type = 'DEFAULT' AND m.menu_id != '02' AND m.menu_id NOT LIKE '02%'
ON CONFLICT DO NOTHING;

-- 4. 添加注释说明
COMMENT ON TABLE t_role IS '角色表，存储系统角色信息';
COMMENT ON TABLE t_user IS '用户表，存储系统用户信息';
COMMENT ON TABLE t_user_group IS '用户组表，存储用户组信息';
COMMENT ON TABLE t_menu IS '菜单表，存储系统菜单信息';
COMMENT ON TABLE t_role_menu IS '角色菜单关联表，存储角色与菜单的关联关系';
COMMENT ON TABLE t_user_role IS '用户角色关联表，存储用户与角色的关联关系';
COMMENT ON TABLE t_user_group_role IS '用户组角色关联表，存储用户组与角色的关联关系';
COMMENT ON TABLE t_user_group_member IS '用户组成员关联表，存储用户组与用户的关联关系';

